;;; test-ultimate-editing.el --- Tests for ultimate editing tools -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides comprehensive tests for the ultimate editing tools.

;;; Code:

(require 'tools/ultimate-editing-tools)

;; Test data creation
(defun create-test-python-file ()
  "Create a test Python file for ultimate editing experiments."
  (let ((test-content "#!/usr/bin/env python3
import os
import sys
from typing import List, Dict

class DataProcessor:
    \"\"\"A class for processing data.\"\"\"
    
    def __init__(self, config_path: str):
        self.config = self.load_config(config_path)
        self.data = []
    
    def load_config(self, path: str) -> Dict:
        # TODO: Implement proper config loading
        return {}
    
    def process_data(self, input_data: List) -> List:
        result = []
        for i in range(len(input_data)):
            item = input_data[i]
            if item > 0:
                processed = item * 2
                result.append(processed)
        return result
    
    def calculate_stats(self, data: List) -> Dict:
        if not data:
            return {}
        
        total = 0
        for item in data:
            total += item
        
        average = total / len(data)
        return {
            'total': total,
            'average': average,
            'count': len(data)
        }

def main():
    processor = DataProcessor('config.json')
    test_data = [1, 2, 3, 4, 5]
    processed = processor.process_data(test_data)
    stats = processor.calculate_stats(processed)
    print(f'Stats: {stats}')

if __name__ == '__main__':
    main()
"))
    (with-temp-file "/tmp/test_ultimate_editing.py"
      (insert test-content))
    (message "Created test Python file: /tmp/test_ultimate_editing.py")))

;; Test functions for ultimate editing tools

(defun test-analyze-code-structure ()
  "Test the analyze_code_structure tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-analyze-code-structure 
                 '((path . "/tmp/test_ultimate_editing.py")
                   (analysis_type . "full")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Code Analysis*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: CODE ANALYSIS ===\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

(defun test-smart-rename ()
  "Test the smart_rename tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-smart-rename
                 '((path . "/tmp/test_ultimate_editing.py")
                   (old_name . "process_data")
                   (new_name . "transform_data")
                   (scope . "file")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Smart Rename*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: SMART RENAME ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "File content after rename:\n")
      (insert-file-contents "/tmp/test_ultimate_editing.py")
      (display-buffer (current-buffer)))))

(defun test-extract-function ()
  "Test the extract_function tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-extract-function
                 '((path . "/tmp/test_ultimate_editing.py")
                   (start_line . 30)
                   (end_line . 35)
                   (function_name . "calculate_sum")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Extract Function*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: EXTRACT FUNCTION ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "File content after extraction:\n")
      (insert-file-contents "/tmp/test_ultimate_editing.py")
      (display-buffer (current-buffer)))))

(defun test-generate-boilerplate ()
  "Test the generate_boilerplate tool."
  (interactive)
  (let ((result (ai-auto-complete-tool-generate-boilerplate
                 '((path . "/tmp/test_boilerplate.py")
                   (template_type . "class")
                   (context . "UserManager")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Generate Boilerplate*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: GENERATE BOILERPLATE ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "Generated content:\n")
      (when (file-exists-p "/tmp/test_boilerplate.py")
        (insert-file-contents "/tmp/test_boilerplate.py"))
      (display-buffer (current-buffer)))))

(defun test-semantic-search-replace ()
  "Test the semantic_search_replace tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-semantic-search-replace
                 '((path . "/tmp/test_ultimate_editing.py")
                   (search_pattern . "for i in range\\(len\\((.+?)\\)\\)")
                   (replace_pattern . "for i, item in enumerate(\\1)")
                   (context_aware . t)))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Semantic Search Replace*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: SEMANTIC SEARCH REPLACE ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "File content after replacement:\n")
      (insert-file-contents "/tmp/test_ultimate_editing.py")
      (display-buffer (current-buffer)))))

(defun test-create-edit-session ()
  "Test the create_edit_session tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-create-edit-session
                 '((path . "/tmp/test_ultimate_editing.py")
                   (session_name . "test_session_2024")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Edit Session*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: CREATE EDIT SESSION ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "Session files created:\n")
      (let ((session-dir "/tmp/edit-sessions/test_session_2024"))
        (when (file-directory-p session-dir)
          (dolist (file (directory-files session-dir t))
            (unless (string-match "\\.$" file)
              (insert (format "- %s\n" (file-name-nondirectory file)))))))
      (display-buffer (current-buffer)))))

(defun test-smart-format ()
  "Test the smart_format tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-smart-format
                 '((path . "/tmp/test_ultimate_editing.py")
                   (style . "pep8")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Smart Format*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: SMART FORMAT ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "Formatted file content:\n")
      (insert-file-contents "/tmp/test_ultimate_editing.py")
      (display-buffer (current-buffer)))))

(defun test-analyze-performance ()
  "Test the analyze_performance tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-analyze-performance
                 '((path . "/tmp/test_ultimate_editing.py")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Performance Analysis*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: PERFORMANCE ANALYSIS ===\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

(defun test-suggest-improvements ()
  "Test the suggest_improvements tool."
  (interactive)
  (create-test-python-file)
  (let ((result (ai-auto-complete-tool-suggest-improvements
                 '((path . "/tmp/test_ultimate_editing.py")
                   (focus_area . "performance")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - AI Suggestions*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: AI-POWERED SUGGESTIONS ===\n\n")
      (insert result)
      (display-buffer (current-buffer)))))

(defun test-intelligent-merge ()
  "Test the intelligent_merge tool."
  (interactive)
  ;; Create test files for merging
  (with-temp-file "/tmp/base.py"
    (insert "def hello():\n    print('Hello')\n\ndef goodbye():\n    print('Goodbye')"))
  
  (with-temp-file "/tmp/version1.py"
    (insert "def hello():\n    print('Hello World')\n\ndef goodbye():\n    print('Goodbye')"))
  
  (with-temp-file "/tmp/version2.py"
    (insert "def hello():\n    print('Hello')\n\ndef goodbye():\n    print('Farewell')"))
  
  (let ((result (ai-auto-complete-tool-intelligent-merge
                 '((base_file . "/tmp/base.py")
                   (file1 . "/tmp/version1.py")
                   (file2 . "/tmp/version2.py")
                   (output_file . "/tmp/merged.py")))))
    (with-current-buffer (get-buffer-create "*Ultimate Test - Intelligent Merge*")
      (erase-buffer)
      (insert "=== ULTIMATE EDITING TEST: INTELLIGENT MERGE ===\n\n")
      (insert "Result: " result "\n\n")
      (insert "Merged content:\n")
      (when (file-exists-p "/tmp/merged.py")
        (insert-file-contents "/tmp/merged.py"))
      (display-buffer (current-buffer)))))

;; Comprehensive test runner
(defun run-all-ultimate-tests ()
  "Run all ultimate editing tool tests."
  (interactive)
  (message "=== RUNNING ULTIMATE EDITING SYSTEM TESTS ===")
  
  (message "\n1. Testing Code Structure Analysis...")
  (test-analyze-code-structure)
  (sit-for 1)
  
  (message "\n2. Testing Smart Rename...")
  (test-smart-rename)
  (sit-for 1)
  
  (message "\n3. Testing Function Extraction...")
  (test-extract-function)
  (sit-for 1)
  
  (message "\n4. Testing Boilerplate Generation...")
  (test-generate-boilerplate)
  (sit-for 1)
  
  (message "\n5. Testing Semantic Search Replace...")
  (test-semantic-search-replace)
  (sit-for 1)
  
  (message "\n6. Testing Edit Session Creation...")
  (test-create-edit-session)
  (sit-for 1)
  
  (message "\n7. Testing Smart Formatting...")
  (test-smart-format)
  (sit-for 1)
  
  (message "\n8. Testing Performance Analysis...")
  (test-analyze-performance)
  (sit-for 1)
  
  (message "\n9. Testing AI Suggestions...")
  (test-suggest-improvements)
  (sit-for 1)
  
  (message "\n10. Testing Intelligent Merge...")
  (test-intelligent-merge)
  
  (message "\n=== ALL ULTIMATE EDITING TESTS COMPLETED ===")
  (message "Check the test result buffers for detailed output."))

;; Interactive test menu
(defun ultimate-editing-test-menu ()
  "Show a menu for testing ultimate editing tools."
  (interactive)
  (let ((choice (read-char-choice 
                 "🚀 ULTIMATE EDITING SYSTEM TESTS:
1. Code Structure Analysis
2. Smart Rename
3. Extract Function
4. Generate Boilerplate
5. Semantic Search Replace
6. Create Edit Session
7. Smart Format
8. Performance Analysis
9. AI Suggestions
0. Intelligent Merge
a. Run ALL Tests
q. Quit

Choose test: " '(?1 ?2 ?3 ?4 ?5 ?6 ?7 ?8 ?9 ?0 ?a ?q))))
    (cond
     ((eq choice ?1) (test-analyze-code-structure))
     ((eq choice ?2) (test-smart-rename))
     ((eq choice ?3) (test-extract-function))
     ((eq choice ?4) (test-generate-boilerplate))
     ((eq choice ?5) (test-semantic-search-replace))
     ((eq choice ?6) (test-create-edit-session))
     ((eq choice ?7) (test-smart-format))
     ((eq choice ?8) (test-analyze-performance))
     ((eq choice ?9) (test-suggest-improvements))
     ((eq choice ?0) (test-intelligent-merge))
     ((eq choice ?a) (run-all-ultimate-tests))
     ((eq choice ?q) (message "Exiting ultimate editing test menu"))
     (t (message "Invalid choice")))))

(provide 'tools/test-ultimate-editing)
;;; test-ultimate-editing.el ends here
