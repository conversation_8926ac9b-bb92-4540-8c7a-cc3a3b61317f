;;; collapsible.el --- Collapsible sections for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides collapsible sections for the chat interface.
;; It allows for folding and unfolding long outputs like code blocks and tool results.

;;; Code:

(require 'cl-lib)

;; Customization options for collapsible sections
(defgroup ai-auto-complete-collapsible nil
  "Settings for collapsible sections in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-collapsible-")

(defcustom ai-auto-complete-collapsible-default-state 'expanded
  "Default state for collapsible sections."
  :type '(choice (const :tag "Expanded" expanded)
                (const :tag "Collapsed" collapsed))
  :group 'ai-auto-complete-collapsible)

(defcustom ai-auto-complete-collapsible-max-lines 20
  "Maximum number of lines to show before making a section collapsible."
  :type 'integer
  :group 'ai-auto-complete-collapsible)

(defcustom ai-auto-complete-collapsible-types
  '(code-block tool-result table)
  "Types of content that should be made collapsible."
  :type '(set (const :tag "Code Blocks" code-block)
             (const :tag "Tool Results" tool-result)
             (const :tag "Tables" table))
  :group 'ai-auto-complete-collapsible)

;; Define faces for collapsible sections
(defface ai-auto-complete-collapsible-header-face
  '((t :foreground "#729fcf" :weight bold :box t))
  "Face for collapsible section headers."
  :group 'ai-auto-complete-collapsible)

(defface ai-auto-complete-collapsible-button-face
  '((t :foreground "#729fcf" :box t))
  "Face for collapsible section buttons."
  :group 'ai-auto-complete-collapsible)

;; Variables for tracking collapsible sections
(defvar-local ai-auto-complete-collapsible-sections nil
  "List of collapsible sections in the current buffer.")

;; Main function to create a collapsible section
(defun ai-auto-complete-collapsible-create (title content &optional type state)
  "Create a collapsible section with TITLE and CONTENT.
TYPE is the type of content (code-block, tool-result, table).
STATE is the initial state (expanded or collapsed)."
  (let* ((section-id (format "section-%d" (random 100000)))
         (initial-state (or state ai-auto-complete-collapsible-default-state))
         (is-expanded (eq initial-state 'expanded))
         (button-text (if is-expanded "[-]" "[+]"))
         (result ""))
    
    ;; Create the header
    (setq result
          (concat result
                  (propertize (format "%s %s\n" button-text title)
                             'face 'ai-auto-complete-collapsible-header-face
                             'ai-auto-complete-collapsible-id section-id
                             'ai-auto-complete-collapsible-state initial-state
                             'ai-auto-complete-collapsible-type type
                             'button t
                             'follow-link t
                             'help-echo (if is-expanded "Click to collapse" "Click to expand")
                             'mouse-face 'highlight
                             'keymap (let ((map (make-sparse-keymap)))
                                      (define-key map [mouse-1] 'ai-auto-complete-collapsible-toggle)
                                      (define-key map (kbd "RET") 'ai-auto-complete-collapsible-toggle)
                                      map))))
    
    ;; Add the content if expanded
    (when is-expanded
      (setq result (concat result content)))
    
    ;; Return the collapsible section
    result))

;; Function to toggle a collapsible section
(defun ai-auto-complete-collapsible-toggle (event)
  "Toggle the collapsible section at EVENT."
  (interactive "e")
  (let* ((pos (if (mouse-event-p event)
                 (posn-point (event-end event))
               (point)))
         (section-id (get-text-property pos 'ai-auto-complete-collapsible-id))
         (state (get-text-property pos 'ai-auto-complete-collapsible-state))
         (type (get-text-property pos 'ai-auto-complete-collapsible-type))
         (inhibit-read-only t))
    
    (when section-id
      ;; Find the section boundaries
      (save-excursion
        (goto-char pos)
        (beginning-of-line)
        (let ((start (point))
              (header-end (line-end-position))
              (next-section-start nil)
              (content ""))
          
          ;; Find the end of this section (start of next section or end of buffer)
          (forward-line 1)
          (let ((content-start (point)))
            (while (and (not (eobp))
                        (not (get-text-property (point) 'ai-auto-complete-collapsible-id)))
              (forward-line 1))
            (setq next-section-start (point))
            
            ;; Toggle the section state
            (if (eq state 'expanded)
                ;; Collapse the section
                (progn
                  ;; Save the content
                  (setq content (buffer-substring-no-properties content-start next-section-start))
                  ;; Delete the content
                  (delete-region content-start next-section-start)
                  ;; Update the header
                  (goto-char start)
                  (let ((header (buffer-substring-no-properties start header-end)))
                    (delete-region start header-end)
                    (insert (propertize (replace-regexp-in-string "\\[-\\]" "[+]" header)
                                       'face 'ai-auto-complete-collapsible-header-face
                                       'ai-auto-complete-collapsible-id section-id
                                       'ai-auto-complete-collapsible-state 'collapsed
                                       'ai-auto-complete-collapsible-type type
                                       'ai-auto-complete-collapsible-content content
                                       'button t
                                       'follow-link t
                                       'help-echo "Click to expand"
                                       'mouse-face 'highlight
                                       'keymap (let ((map (make-sparse-keymap)))
                                                (define-key map [mouse-1] 'ai-auto-complete-collapsible-toggle)
                                                (define-key map (kbd "RET") 'ai-auto-complete-collapsible-toggle)
                                                map)))))
              
              ;; Expand the section
              (let ((content (get-text-property pos 'ai-auto-complete-collapsible-content)))
                ;; Update the header
                (goto-char start)
                (let ((header (buffer-substring-no-properties start header-end)))
                  (delete-region start header-end)
                  (insert (propertize (replace-regexp-in-string "\\[\\+\\]" "[-]" header)
                                     'face 'ai-auto-complete-collapsible-header-face
                                     'ai-auto-complete-collapsible-id section-id
                                     'ai-auto-complete-collapsible-state 'expanded
                                     'ai-auto-complete-collapsible-type type
                                     'button t
                                     'follow-link t
                                     'help-echo "Click to collapse"
                                     'mouse-face 'highlight
                                     'keymap (let ((map (make-sparse-keymap)))
                                              (define-key map [mouse-1] 'ai-auto-complete-collapsible-toggle)
                                              (define-key map (kbd "RET") 'ai-auto-complete-collapsible-toggle)
                                              map))))
                ;; Insert the content
                (goto-char (line-end-position))
                (insert "\n" content)))))))))

(provide 'ui/collapsible)
;;; collapsible.el ends here
