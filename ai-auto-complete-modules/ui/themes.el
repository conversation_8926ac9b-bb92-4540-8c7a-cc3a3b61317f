;;; themes.el --- Themes for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides theming functionality for the chat interface.
;; It allows for customizing colors, fonts, and spacing.

;;; Code:

(require 'cl-lib)

;; Customization options for themes
(defgroup ai-auto-complete-themes nil
  "Settings for themes in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-themes-")

(defcustom ai-auto-complete-themes-current-theme 'default
  "Current theme for the AI Auto Complete interface."
  :type '(choice (const :tag "Default" default)
                (const :tag "Dark" dark)
                (const :tag "Light" light)
                (const :tag "Solarized Dark" solarized-dark)
                (const :tag "Solarized Light" solarized-light)
                (const :tag "Nord" nord)
                (symbol :tag "Custom"))
  :group 'ai-auto-complete-themes
  :set (lambda (symbol value)
         (set-default symbol value)
         (when (fboundp 'ai-auto-complete-themes-apply)
           (ai-auto-complete-themes-apply value))))

(defcustom ai-auto-complete-themes-font-family nil
  "Font family for the AI Auto Complete interface.
If nil, use the default font."
  :type '(choice (const :tag "Default" nil)
                (string :tag "Font Family"))
  :group 'ai-auto-complete-themes)

(defcustom ai-auto-complete-themes-font-size nil
  "Font size for the AI Auto Complete interface.
If nil, use the default font size."
  :type '(choice (const :tag "Default" nil)
                (integer :tag "Font Size"))
  :group 'ai-auto-complete-themes)

(defcustom ai-auto-complete-themes-line-spacing 0.2
  "Line spacing for the AI Auto Complete interface."
  :type 'float
  :group 'ai-auto-complete-themes)

;; Theme definitions
(defvar ai-auto-complete-themes-definitions
  '((default
      :background "#2e3436"
      :foreground "#eeeeec"
      :user-fg "#729fcf"
      :assistant-fg "#8ae234"
      :system-fg "#eeeeec"
      :agent-fg "#fcaf3e"
      :tool-fg "#ad7fa8"
      :tool-result-fg "#729fcf"
      :timestamp-fg "#888a85"
      :button-fg "#729fcf"
      :button-bg "#eeeeec"
      :header-fg "#3465a4"
      :header-bg "#eeeeec"
      :code-block-bg "#f5f5f5"
      :code-block-fg "#333333"
      :blockquote-bg "#f9f9f9"
      :blockquote-fg "#777777"
      :link-fg "blue"
      :border-color "#888a85")
    
    (dark
      :background "#1e1e1e"
      :foreground "#d4d4d4"
      :user-fg "#569cd6"
      :assistant-fg "#6a9955"
      :system-fg "#d4d4d4"
      :agent-fg "#ce9178"
      :tool-fg "#c586c0"
      :tool-result-fg "#4ec9b0"
      :timestamp-fg "#6a9955"
      :button-fg "#569cd6"
      :button-bg "#2d2d2d"
      :header-fg "#569cd6"
      :header-bg "#2d2d2d"
      :code-block-bg "#2d2d2d"
      :code-block-fg "#d4d4d4"
      :blockquote-bg "#2d2d2d"
      :blockquote-fg "#6a9955"
      :link-fg "#569cd6"
      :border-color "#444444")
    
    (light
      :background "#ffffff"
      :foreground "#000000"
      :user-fg "#0000ff"
      :assistant-fg "#008000"
      :system-fg "#000000"
      :agent-fg "#ff8c00"
      :tool-fg "#800080"
      :tool-result-fg "#0000ff"
      :timestamp-fg "#808080"
      :button-fg "#0000ff"
      :button-bg "#f0f0f0"
      :header-fg "#0000ff"
      :header-bg "#f0f0f0"
      :code-block-bg "#f5f5f5"
      :code-block-fg "#000000"
      :blockquote-bg "#f9f9f9"
      :blockquote-fg "#808080"
      :link-fg "#0000ff"
      :border-color "#cccccc")
    
    (solarized-dark
      :background "#002b36"
      :foreground "#839496"
      :user-fg "#268bd2"
      :assistant-fg "#859900"
      :system-fg "#839496"
      :agent-fg "#cb4b16"
      :tool-fg "#6c71c4"
      :tool-result-fg "#2aa198"
      :timestamp-fg "#586e75"
      :button-fg "#268bd2"
      :button-bg "#073642"
      :header-fg "#268bd2"
      :header-bg "#073642"
      :code-block-bg "#073642"
      :code-block-fg "#839496"
      :blockquote-bg "#073642"
      :blockquote-fg "#586e75"
      :link-fg "#268bd2"
      :border-color "#586e75")
    
    (solarized-light
      :background "#fdf6e3"
      :foreground "#657b83"
      :user-fg "#268bd2"
      :assistant-fg "#859900"
      :system-fg "#657b83"
      :agent-fg "#cb4b16"
      :tool-fg "#6c71c4"
      :tool-result-fg "#2aa198"
      :timestamp-fg "#93a1a1"
      :button-fg "#268bd2"
      :button-bg "#eee8d5"
      :header-fg "#268bd2"
      :header-bg "#eee8d5"
      :code-block-bg "#eee8d5"
      :code-block-fg "#657b83"
      :blockquote-bg "#eee8d5"
      :blockquote-fg "#93a1a1"
      :link-fg "#268bd2"
      :border-color "#93a1a1")
    
    (nord
      :background "#2e3440"
      :foreground "#d8dee9"
      :user-fg "#88c0d0"
      :assistant-fg "#a3be8c"
      :system-fg "#d8dee9"
      :agent-fg "#d08770"
      :tool-fg "#b48ead"
      :tool-result-fg "#8fbcbb"
      :timestamp-fg "#4c566a"
      :button-fg "#88c0d0"
      :button-bg "#3b4252"
      :header-fg "#88c0d0"
      :header-bg "#3b4252"
      :code-block-bg "#3b4252"
      :code-block-fg "#d8dee9"
      :blockquote-bg "#3b4252"
      :blockquote-fg "#4c566a"
      :link-fg "#88c0d0"
      :border-color "#4c566a"))
  "Theme definitions for AI Auto Complete.")

;; Function to get a theme property
(defun ai-auto-complete-themes-get-property (property &optional theme)
  "Get PROPERTY from THEME.
If THEME is nil, use the current theme."
  (let* ((theme-name (or theme ai-auto-complete-themes-current-theme))
         (theme-def (assoc theme-name ai-auto-complete-themes-definitions)))
    (if theme-def
        (plist-get (cdr theme-def) property)
      ;; If theme not found, use default
      (plist-get (cdr (assoc 'default ai-auto-complete-themes-definitions)) property))))

;; Function to apply a theme
(defun ai-auto-complete-themes-apply (&optional theme)
  "Apply THEME to the AI Auto Complete interface.
If THEME is nil, use the current theme."
  (interactive)
  (let ((theme-name (or theme ai-auto-complete-themes-current-theme)))
    ;; Update faces
    (custom-set-faces
     `(ai-auto-complete-user-face ((t :foreground ,(ai-auto-complete-themes-get-property :user-fg theme-name) :weight bold)))
     `(ai-auto-complete-assistant-face ((t :foreground ,(ai-auto-complete-themes-get-property :assistant-fg theme-name) :weight bold)))
     `(ai-auto-complete-system-face ((t :foreground ,(ai-auto-complete-themes-get-property :system-fg theme-name) :slant italic)))
     `(ai-auto-complete-timestamp-face ((t :foreground ,(ai-auto-complete-themes-get-property :timestamp-fg theme-name) :slant italic :height 0.8)))
     `(ai-auto-complete-enhanced-chat-agent-face ((t :foreground ,(ai-auto-complete-themes-get-property :agent-fg theme-name) :weight bold)))
     `(ai-auto-complete-enhanced-chat-tool-face ((t :foreground ,(ai-auto-complete-themes-get-property :tool-fg theme-name) :weight bold)))
     `(ai-auto-complete-enhanced-chat-tool-result-face ((t :foreground ,(ai-auto-complete-themes-get-property :tool-result-fg theme-name) :weight bold)))
     `(ai-auto-complete-enhanced-chat-button-face ((t :box t :foreground ,(ai-auto-complete-themes-get-property :button-fg theme-name) :background ,(ai-auto-complete-themes-get-property :button-bg theme-name))))
     `(ai-auto-complete-enhanced-chat-header-face ((t :height 1.2 :weight bold :foreground ,(ai-auto-complete-themes-get-property :header-fg theme-name) :background ,(ai-auto-complete-themes-get-property :header-bg theme-name))))
     `(ai-auto-complete-enhanced-chat-timestamp-face ((t :foreground ,(ai-auto-complete-themes-get-property :timestamp-fg theme-name) :slant italic :height 0.8)))
     `(ai-auto-complete-markdown-code-face ((t :inherit fixed-pitch :background ,(ai-auto-complete-themes-get-property :code-block-bg theme-name) :foreground ,(ai-auto-complete-themes-get-property :code-block-fg theme-name))))
     `(ai-auto-complete-markdown-code-block-face ((t :inherit fixed-pitch :background ,(ai-auto-complete-themes-get-property :code-block-bg theme-name))))
     `(ai-auto-complete-markdown-blockquote-face ((t :slant italic :foreground ,(ai-auto-complete-themes-get-property :blockquote-fg theme-name) :background ,(ai-auto-complete-themes-get-property :blockquote-bg theme-name))))
     `(ai-auto-complete-markdown-link-face ((t :foreground ,(ai-auto-complete-themes-get-property :link-fg theme-name) :underline t)))
     `(ai-auto-complete-collapsible-header-face ((t :foreground ,(ai-auto-complete-themes-get-property :button-fg theme-name) :weight bold :box t)))
     `(ai-auto-complete-collapsible-button-face ((t :foreground ,(ai-auto-complete-themes-get-property :button-fg theme-name) :box t))))
    
    ;; Apply font settings if specified
    (when (and ai-auto-complete-themes-font-family
               (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
      (with-current-buffer ai-auto-complete-enhanced-chat-buffer-name
        (buffer-face-set `(:family ,ai-auto-complete-themes-font-family))))
    
    (when (and ai-auto-complete-themes-font-size
               (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
      (with-current-buffer ai-auto-complete-enhanced-chat-buffer-name
        (text-scale-set ai-auto-complete-themes-font-size)))
    
    ;; Apply line spacing
    (when (get-buffer ai-auto-complete-enhanced-chat-buffer-name)
      (with-current-buffer ai-auto-complete-enhanced-chat-buffer-name
        (setq-local line-spacing ai-auto-complete-themes-line-spacing)))
    
    ;; Refresh the chat interface if it exists
    (when (and (fboundp 'ai-auto-complete-enhanced-chat-refresh)
               (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
      (with-current-buffer ai-auto-complete-enhanced-chat-buffer-name
        (ai-auto-complete-enhanced-chat-refresh)))
    
    (message "Applied theme: %s" theme-name)))

(provide 'ui/themes)
;;; themes.el ends here
