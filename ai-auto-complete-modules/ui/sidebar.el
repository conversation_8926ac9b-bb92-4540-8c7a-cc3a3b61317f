;;; sidebar.el --- Sidebar for agent management and context display -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides a sidebar for agent management and context display.
;; It allows users to select agents, manage context, and control the chat interface.

;;; Code:

(require 'cl-lib)
(require 'ui/enhanced-chat)

;; Customization options for the sidebar
(defgroup ai-auto-complete-sidebar nil
  "Settings for the sidebar in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-sidebar-")

(defcustom ai-auto-complete-sidebar-width 30
  "Width of the sidebar in characters."
  :type 'integer
  :group 'ai-auto-complete-sidebar)

(defcustom ai-auto-complete-sidebar-position 'left
  "Position of the sidebar relative to the chat window."
  :type '(choice (const :tag "Left" left)
                (const :tag "Right" right))
  :group 'ai-auto-complete-sidebar)

(defcustom ai-auto-complete-sidebar-show-agents t
  "Whether to show agents in the sidebar."
  :type 'boolean
  :group 'ai-auto-complete-sidebar)

(defcustom ai-auto-complete-sidebar-show-context t
  "Whether to show context in the sidebar."
  :type 'boolean
  :group 'ai-auto-complete-sidebar)

(defcustom ai-auto-complete-sidebar-show-tools t
  "Whether to show tools in the sidebar."
  :type 'boolean
  :group 'ai-auto-complete-sidebar)

;; Variables for tracking sidebar state
(defvar-local ai-auto-complete-sidebar-buffer nil
  "Buffer for the sidebar.")

(defvar-local ai-auto-complete-sidebar-window nil
  "Window for the sidebar.")

(defvar-local ai-auto-complete-sidebar-chat-buffer nil
  "Associated chat buffer for the sidebar.")

(defvar-local ai-auto-complete-sidebar-selected-agent nil
  "Currently selected agent in the sidebar.")

(defvar-local ai-auto-complete-sidebar-selected-tool nil
  "Currently selected tool in the sidebar.")

;; Define the sidebar mode map
(defvar ai-auto-complete-sidebar-mode-map
  (let ((map (make-sparse-keymap)))
    (define-key map (kbd "RET") 'ai-auto-complete-sidebar-activate-item)
    (define-key map (kbd "q") 'ai-auto-complete-sidebar-close)
    (define-key map (kbd "r") 'ai-auto-complete-sidebar-refresh)
    (define-key map (kbd "a") 'ai-auto-complete-sidebar-toggle-agents)
    (define-key map (kbd "c") 'ai-auto-complete-sidebar-toggle-context)
    (define-key map (kbd "t") 'ai-auto-complete-sidebar-toggle-tools)
    map)
  "Keymap for the sidebar.")

;; Define the sidebar mode
(define-derived-mode ai-auto-complete-sidebar-mode special-mode "AI-Sidebar"
  "Major mode for the AI Auto Complete sidebar."
  :group 'ai-auto-complete-sidebar
  (setq buffer-read-only t)
  (setq truncate-lines t)
  (setq line-spacing 0.2)
  (buffer-disable-undo))

;; Main function to show the sidebar
(defun ai-auto-complete-sidebar-show ()
  "Show the sidebar for agent management and context display."
  (interactive)
  (let* ((chat-buffer (get-buffer ai-auto-complete-enhanced-chat-buffer-name))
         (sidebar-buffer-name (format "*AI Auto Complete Sidebar - %s*"
                                     (buffer-name chat-buffer)))
         (sidebar-buffer (get-buffer-create sidebar-buffer-name))
         (sidebar-window nil))

    ;; Set up the sidebar buffer
    (with-current-buffer sidebar-buffer
      (ai-auto-complete-sidebar-mode)
      (setq ai-auto-complete-sidebar-chat-buffer chat-buffer)

      ;; Create the sidebar window
      (setq sidebar-window
            (display-buffer-in-side-window
             sidebar-buffer
             `((side . ,(if (eq ai-auto-complete-sidebar-position 'left) 'left 'right))
               (window-width . ,ai-auto-complete-sidebar-width))))

      (setq ai-auto-complete-sidebar-window sidebar-window)
      (setq ai-auto-complete-sidebar-buffer sidebar-buffer)

      ;; Populate the sidebar
      (ai-auto-complete-sidebar-refresh))

    ;; Return the sidebar window
    sidebar-window))

;; Function to close the sidebar
(defun ai-auto-complete-sidebar-close ()
  "Close the sidebar."
  (interactive)
  (when (and ai-auto-complete-sidebar-window
             (window-live-p ai-auto-complete-sidebar-window))
    (delete-window ai-auto-complete-sidebar-window)
    (setq ai-auto-complete-sidebar-window nil)))

;; Function to refresh the sidebar
(defun ai-auto-complete-sidebar-refresh ()
  "Refresh the sidebar content."
  (interactive)
  (when (buffer-live-p ai-auto-complete-sidebar-buffer)
    (with-current-buffer ai-auto-complete-sidebar-buffer
      (let ((inhibit-read-only t))
        ;; Clear the buffer
        (erase-buffer)

        ;; Add header
        (insert (propertize "AI Auto Complete\n"
                           'face '(:height 1.2 :weight bold :foreground "#3465a4")
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize (format-time-string "%Y-%m-%d %H:%M:%S\n")
                           'face '(:slant italic :foreground "#888a85")
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize (make-string (window-width) ?─)
                           'face '(:foreground "#888a85")
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert "\n")

        ;; Add agent section
        (when ai-auto-complete-sidebar-show-agents
          (ai-auto-complete-sidebar-insert-agents))

        ;; Add context section
        (when ai-auto-complete-sidebar-show-context
          (ai-auto-complete-sidebar-insert-context))

        ;; Add tools section
        (when ai-auto-complete-sidebar-show-tools
          (ai-auto-complete-sidebar-insert-tools))

        ;; Add footer
        (goto-char (point-max))
        (insert "\n")
        (insert (propertize (make-string (window-width) ?─)
                           'face '(:foreground "#888a85")
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert "\n")
        (insert (propertize "q: close, r: refresh, a/c/t: toggle sections"
                           'face '(:slant italic :foreground "#888a85")
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))))))

;; Function to insert agents section
(defun ai-auto-complete-sidebar-insert-agents ()
  "Insert the agents section in the sidebar."
  (insert (propertize "AGENTS\n"
                     'face '(:weight bold :foreground "#729fcf")
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t))

  ;; Check if agents are available
  (if (and (boundp 'ai-auto-complete-agents)
           (hash-table-p ai-auto-complete-agents)
           (> (hash-table-count ai-auto-complete-agents) 0))
      (progn
        ;; Insert each agent
        (maphash (lambda (name agent)
                   (let ((selected (and ai-auto-complete-sidebar-selected-agent
                                       (string= name ai-auto-complete-sidebar-selected-agent))))
                     (insert (propertize (format "%s %s\n"
                                                (if selected "→" " ")
                                                name)
                                        'face (if selected
                                                 '(:weight bold :foreground "#8ae234")
                                               '(:foreground "#eeeeec"))
                                        'ai-auto-complete-sidebar-agent name
                                        'read-only t
                                        'front-sticky t
                                        'rear-nonsticky t))))
                 ai-auto-complete-agents))
    ;; No agents available
    (insert (propertize "  No agents available\n"
                       'face '(:slant italic :foreground "#888a85")
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t)))

  (insert "\n"))

;; Function to insert context section
(defun ai-auto-complete-sidebar-insert-context ()
  "Insert the context section in the sidebar."
  (insert (propertize "CONTEXT\n"
                     'face '(:weight bold :foreground "#729fcf")
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t))

  ;; Check if context is available
  (if (and (boundp 'ai-auto-complete-context-files)
           (> (length ai-auto-complete-context-files) 0))
      (progn
        ;; Insert each context file
        (dolist (file ai-auto-complete-context-files)
          (let ((file-name (file-name-nondirectory file)))
            (insert (propertize (format "  %s\n" file-name)
                               'face '(:foreground "#eeeeec")
                               'ai-auto-complete-sidebar-context file
                               'help-echo file
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t)))))
    ;; No context available
    (insert (propertize "  No context files\n"
                       'face '(:slant italic :foreground "#888a85")
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t)))

  ;; Add buttons for context management
  (insert (propertize "  "
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t))
  (insert-text-button "[Add File]"
                     'face '(:box t :foreground "#729fcf")
                     'action 'ai-auto-complete-sidebar-add-context-file
                     'follow-link t
                     'help-echo "Add a file to context")
  (insert (propertize " "
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t))
  (insert-text-button "[Clear]"
                     'face '(:box t :foreground "#729fcf")
                     'action 'ai-auto-complete-sidebar-clear-context
                     'follow-link t
                     'help-echo "Clear all context")
  (insert "\n\n"))

;; Function to insert tools section
(defun ai-auto-complete-sidebar-insert-tools ()
  "Insert the tools section in the sidebar."
  (insert (propertize "TOOLS\n"
                     'face '(:weight bold :foreground "#729fcf")
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t))

  ;; Check if tools are available
  (if (and (boundp 'ai-auto-complete-tools)
           (hash-table-p ai-auto-complete-tools)
           (> (hash-table-count ai-auto-complete-tools) 0))
      (progn
        ;; Insert each tool
        (let ((tools-list '()))
          ;; Collect tools in a list for sorting
          (maphash (lambda (name tool)
                     (push name tools-list))
                   ai-auto-complete-tools)

          ;; Sort tools alphabetically
          (setq tools-list (sort tools-list 'string<))

          ;; Insert each tool
          (dolist (name tools-list)
            (let* ((tool (gethash name ai-auto-complete-tools))
                   (description (plist-get tool :description))
                   (selected (and ai-auto-complete-sidebar-selected-tool
                                 (string= name ai-auto-complete-sidebar-selected-tool))))
              (insert (propertize (format "%s %s\n"
                                         (if selected "→" " ")
                                         name)
                                 'face (if selected
                                          '(:weight bold :foreground "#8ae234")
                                        '(:foreground "#eeeeec"))
                                 'ai-auto-complete-sidebar-tool name
                                 'help-echo description
                                 'read-only t
                                 'front-sticky t
                                 'rear-nonsticky t))))))
    ;; No tools available
    (insert (propertize "  No tools available\n"
                       'face '(:slant italic :foreground "#888a85")
                       'read-only t
                       'front-sticky t
                       'rear-nonsticky t)))

  ;; Add buttons for tool management
  (insert (propertize "  "
                     'read-only t
                     'front-sticky t
                     'rear-nonsticky t))
  (insert-text-button "[Enable/Disable]"
                     'face '(:box t :foreground "#729fcf")
                     'action 'ai-auto-complete-sidebar-toggle-tools-enabled
                     'follow-link t
                     'help-echo "Enable or disable tools")
  (insert "\n\n"))

;; Function to activate an item in the sidebar
(defun ai-auto-complete-sidebar-activate-item ()
  "Activate the item at point in the sidebar."
  (interactive)
  (let ((agent (get-text-property (point) 'ai-auto-complete-sidebar-agent))
        (context (get-text-property (point) 'ai-auto-complete-sidebar-context))
        (tool (get-text-property (point) 'ai-auto-complete-sidebar-tool)))
    (cond
     ;; Activate agent
     (agent
      (setq ai-auto-complete-sidebar-selected-agent agent)
      (message "Selected agent: %s" agent)
      (ai-auto-complete-sidebar-refresh))

     ;; Activate context
     (context
      (when (fboundp 'ai-auto-complete-remove-from-context)
        (ai-auto-complete-remove-from-context context)
        (message "Removed from context: %s" context)
        (ai-auto-complete-sidebar-refresh)))

     ;; Activate tool
     (tool
      (setq ai-auto-complete-sidebar-selected-tool tool)
      (message "Selected tool: %s" tool)
      (ai-auto-complete-sidebar-refresh)))))

;; Functions for context management
(defun ai-auto-complete-sidebar-add-context-file (button)
  "Add a file to context."
  (when (fboundp 'ai-auto-complete-add-file-to-context)
    (call-interactively 'ai-auto-complete-add-file-to-context)
    (ai-auto-complete-sidebar-refresh)))

(defun ai-auto-complete-sidebar-clear-context (button)
  "Clear all context."
  (when (and (boundp 'ai-auto-complete-context-files)
             (yes-or-no-p "Clear all context files? "))
    (setq ai-auto-complete-context-files nil)
    (message "Context cleared")
    (ai-auto-complete-sidebar-refresh)))

;; Functions for toggling sections
(defun ai-auto-complete-sidebar-toggle-agents ()
  "Toggle the agents section in the sidebar."
  (interactive)
  (setq ai-auto-complete-sidebar-show-agents
        (not ai-auto-complete-sidebar-show-agents))
  (ai-auto-complete-sidebar-refresh))

(defun ai-auto-complete-sidebar-toggle-context ()
  "Toggle the context section in the sidebar."
  (interactive)
  (setq ai-auto-complete-sidebar-show-context
        (not ai-auto-complete-sidebar-show-context))
  (ai-auto-complete-sidebar-refresh))

(defun ai-auto-complete-sidebar-toggle-tools ()
  "Toggle the tools section in the sidebar."
  (interactive)
  (setq ai-auto-complete-sidebar-show-tools
        (not ai-auto-complete-sidebar-show-tools))
  (ai-auto-complete-sidebar-refresh))

(defun ai-auto-complete-sidebar-toggle-tools-enabled (button)
  "Toggle whether tools are enabled."
  (when (boundp 'ai-auto-complete-tools-enabled)
    (setq ai-auto-complete-tools-enabled
          (not ai-auto-complete-tools-enabled))
    (message "Tools %s"
             (if ai-auto-complete-tools-enabled "enabled" "disabled"))
    (ai-auto-complete-sidebar-refresh)))

(provide 'ui/sidebar)
;;; sidebar.el ends here
