;;; native-streaming.el --- Native streaming for LLM backends -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides native streaming support for LLM backends that support it.
;; It integrates with OpenAI, Anthropic, and other streaming-capable backends.

;;; Code:

(require 'cl-lib)
(require 'ui/streaming)

;; Customization options for native streaming
(defgroup ai-auto-complete-native-streaming nil
  "Settings for native streaming in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-native-streaming-")

(defcustom ai-auto-complete-native-streaming-enabled t
  "Whether native streaming is enabled for supported backends."
  :type 'boolean
  :group 'ai-auto-complete-native-streaming)

(defcustom ai-auto-complete-native-streaming-backends
  '(openai anthropic openrouter)
  "Backends that support native streaming."
  :type '(set (const :tag "OpenAI" openai)
             (const :tag "Anthropic" anthropic)
             (const :tag "OpenRouter" openrouter))
  :group 'ai-auto-complete-native-streaming)

;; Variables for tracking native streaming state
(defvar-local ai-auto-complete-native-streaming-process nil
  "Process for native streaming.")

(defvar-local ai-auto-complete-native-streaming-buffer nil
  "Buffer for native streaming output.")

(defvar-local ai-auto-complete-native-streaming-callback nil
  "Callback function for native streaming.")

(defvar-local ai-auto-complete-native-streaming-error-callback nil
  "Error callback function for native streaming.")

;; Variables for accumulating content in native streaming
(defvar-local ai-auto-complete-native-streaming-accumulated-content ""
  "Content accumulated since the last streaming update.")

(defvar-local ai-auto-complete-native-streaming-last-update-time nil
  "Timestamp of the last streaming update.")

;; OpenAI streaming integration

(defun ai-auto-complete-native-streaming-openai (prompt callback error-callback)
  "Stream a response from OpenAI for PROMPT.
CALLBACK is called with each chunk of the response.
ERROR-CALLBACK is called if an error occurs."
  (when (and ai-auto-complete-native-streaming-enabled
             (memq 'openai ai-auto-complete-native-streaming-backends))

    ;; Start streaming
    (ai-auto-complete-streaming-start 'assistant)

    ;; Set up the streaming state
    (setq ai-auto-complete-native-streaming-callback callback
          ai-auto-complete-native-streaming-error-callback error-callback
          ai-auto-complete-native-streaming-accumulated-content ""
          ai-auto-complete-native-streaming-last-update-time nil)

    ;; Create a buffer for the streaming output
    (setq ai-auto-complete-native-streaming-buffer
          (generate-new-buffer " *openai-streaming*"))

    ;; Prepare the request
    (let* ((api-key (or (getenv "OPENAI_API_KEY")
                        ai-auto-complete-openai-api-key))
           (model (or ai-auto-complete-openai-model "gpt-4"))
           (url "https://api.openai.com/v1/chat/completions")
           (headers `(("Content-Type" . "application/json")
                      ("Authorization" . ,(concat "Bearer " api-key))))
           (data (json-encode
                  `((model . ,model)
                    (messages . ,(ai-auto-complete-native-streaming-format-prompt prompt))
                    (stream . t))))
           (command (format "curl -s -X POST -H \"Content-Type: application/json\" -H \"Authorization: Bearer %s\" -d '%s' %s"
                           api-key data url)))

      ;; Start the streaming process
      (setq ai-auto-complete-native-streaming-process
            (start-process-shell-command
             "openai-streaming" ai-auto-complete-native-streaming-buffer command))

      ;; Set up a process filter to handle the streaming output
      (set-process-filter ai-auto-complete-native-streaming-process
                          'ai-auto-complete-native-streaming-openai-filter)

      ;; Set up a process sentinel to handle completion or errors
      (set-process-sentinel ai-auto-complete-native-streaming-process
                           'ai-auto-complete-native-streaming-openai-sentinel))))

(defun ai-auto-complete-native-streaming-openai-filter (process output)
  "Filter function for OpenAI streaming process.
PROCESS is the streaming process.
OUTPUT is the latest output from the process."
  (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
    (with-current-buffer ai-auto-complete-native-streaming-buffer
      ;; Process each line of the output
      (dolist (line (split-string output "\n" t))
        (when (string-match "^data: " line)
          (let ((data (substring line 6)))
            (unless (string= data "[DONE]")
              (condition-case nil
                  (let* ((json-object-type 'plist)
                         (json-array-type 'list)
                         (json-key-type 'symbol)
                         (json-data (json-read-from-string data))
                         (choices (plist-get json-data :choices))
                         (delta (plist-get (car choices) :delta))
                         (content (plist-get delta :content)))
                    (when content
                      ;; Accumulate content
                      (setq ai-auto-complete-native-streaming-accumulated-content
                            (concat ai-auto-complete-native-streaming-accumulated-content content))

                      ;; Only update the display if enough content has accumulated or enough time has passed
                      (let ((current-time (current-time)))
                        (when (or (not ai-auto-complete-native-streaming-last-update-time)
                                 (> (length ai-auto-complete-native-streaming-accumulated-content)
                                    ai-auto-complete-streaming-min-chunk-size)
                                 (> (float-time (time-subtract current-time
                                                              ai-auto-complete-native-streaming-last-update-time))
                                    ai-auto-complete-streaming-max-update-frequency))
                          ;; Update the streaming display
                          (ai-auto-complete-streaming-update ai-auto-complete-native-streaming-accumulated-content)

                          ;; Reset accumulated content
                          (setq ai-auto-complete-native-streaming-accumulated-content "")

                          ;; Update timestamp
                          (setq ai-auto-complete-native-streaming-last-update-time current-time)))

                      ;; Call the callback if provided
                      (when (functionp ai-auto-complete-native-streaming-callback)
                        (funcall ai-auto-complete-native-streaming-callback content))))
                (error nil)))))))))

(defun ai-auto-complete-native-streaming-openai-sentinel (process event)
  "Sentinel function for OpenAI streaming process.
PROCESS is the streaming process.
EVENT is the process event."
  (cond
   ((string-match "finished" event)
    ;; Process completed successfully
    ;; Update any remaining accumulated content
    (when (and (boundp 'ai-auto-complete-native-streaming-accumulated-content)
               (not (string-empty-p ai-auto-complete-native-streaming-accumulated-content)))
      (ai-auto-complete-streaming-update ai-auto-complete-native-streaming-accumulated-content)
      (setq ai-auto-complete-native-streaming-accumulated-content ""))

    (ai-auto-complete-streaming-complete)

    ;; Clean up
    (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
      (kill-buffer ai-auto-complete-native-streaming-buffer))

    (setq ai-auto-complete-native-streaming-process nil
          ai-auto-complete-native-streaming-buffer nil
          ai-auto-complete-native-streaming-callback nil
          ai-auto-complete-native-streaming-error-callback nil))

   ((string-match "exited abnormally" event)
    ;; Process failed
    (let ((error-message "OpenAI streaming failed"))
      ;; Get the error message from the buffer if possible
      (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
        (with-current-buffer ai-auto-complete-native-streaming-buffer
          (setq error-message (buffer-string))
          (kill-buffer)))

      ;; Call the error callback if provided
      (when (functionp ai-auto-complete-native-streaming-error-callback)
        (funcall ai-auto-complete-native-streaming-error-callback error-message))

      ;; Complete the streaming with an error message
      (ai-auto-complete-streaming-update (format "\n\nError: %s" error-message))
      (ai-auto-complete-streaming-complete)

      ;; Clean up
      (setq ai-auto-complete-native-streaming-process nil
            ai-auto-complete-native-streaming-buffer nil
            ai-auto-complete-native-streaming-callback nil
            ai-auto-complete-native-streaming-error-callback nil)))))

;; Anthropic streaming integration

(defun ai-auto-complete-native-streaming-anthropic (prompt callback error-callback)
  "Stream a response from Anthropic for PROMPT.
CALLBACK is called with each chunk of the response.
ERROR-CALLBACK is called if an error occurs."
  (when (and ai-auto-complete-native-streaming-enabled
             (memq 'anthropic ai-auto-complete-native-streaming-backends))

    ;; Start streaming
    (ai-auto-complete-streaming-start 'assistant)

    ;; Set up the streaming state
    (setq ai-auto-complete-native-streaming-callback callback
          ai-auto-complete-native-streaming-error-callback error-callback
          ai-auto-complete-native-streaming-accumulated-content ""
          ai-auto-complete-native-streaming-last-update-time nil)

    ;; Create a buffer for the streaming output
    (setq ai-auto-complete-native-streaming-buffer
          (generate-new-buffer " *anthropic-streaming*"))

    ;; Prepare the request
    (let* ((api-key (or (getenv "ANTHROPIC_API_KEY")
                        ai-auto-complete-anthropic-api-key))
           (model (or ai-auto-complete-anthropic-model "claude-3-opus-20240229"))
           (url "https://api.anthropic.com/v1/messages")
           (headers `(("Content-Type" . "application/json")
                      ("x-api-key" . ,api-key)
                      ("anthropic-version" . "2023-06-01")))
           (system-prompt (when (boundp 'ai-auto-complete-system-prompt)
                           ai-auto-complete-system-prompt))
           (formatted-prompt (ai-auto-complete-native-streaming-format-anthropic-prompt prompt))
           (data (json-encode
                  `((model . ,model)
                    (messages . ,formatted-prompt)
                    ,@(when system-prompt
                        `((system . ,system-prompt)))
                    (stream . t))))
           (command (format "curl -s -X POST -H \"Content-Type: application/json\" -H \"x-api-key: %s\" -H \"anthropic-version: 2023-06-01\" -d '%s' %s"
                           api-key data url)))

      ;; Start the streaming process
      (setq ai-auto-complete-native-streaming-process
            (start-process-shell-command
             "anthropic-streaming" ai-auto-complete-native-streaming-buffer command))

      ;; Set up a process filter to handle the streaming output
      (set-process-filter ai-auto-complete-native-streaming-process
                          'ai-auto-complete-native-streaming-anthropic-filter)

      ;; Set up a process sentinel to handle completion or errors
      (set-process-sentinel ai-auto-complete-native-streaming-process
                           'ai-auto-complete-native-streaming-anthropic-sentinel))))

(defun ai-auto-complete-native-streaming-anthropic-filter (process output)
  "Filter function for Anthropic streaming process.
PROCESS is the streaming process.
OUTPUT is the latest output from the process."
  (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
    (with-current-buffer ai-auto-complete-native-streaming-buffer
      ;; Process each line of the output
      (dolist (line (split-string output "\n" t))
        (when (string-match "^data: " line)
          (let ((data (substring line 6)))
            (unless (string= data "[DONE]")
              (condition-case nil
                  (let* ((json-object-type 'plist)
                         (json-array-type 'list)
                         (json-key-type 'symbol)
                         (json-data (json-read-from-string data))
                         (type (plist-get json-data :type))
                         (content-block (plist-get json-data :delta))
                         (content (plist-get content-block :text)))
                    (when (and (string= type "content_block_delta") content)
                      ;; Accumulate content
                      (setq ai-auto-complete-native-streaming-accumulated-content
                            (concat ai-auto-complete-native-streaming-accumulated-content content))

                      ;; Only update the display if enough content has accumulated or enough time has passed
                      (let ((current-time (current-time)))
                        (when (or (not ai-auto-complete-native-streaming-last-update-time)
                                 (> (length ai-auto-complete-native-streaming-accumulated-content)
                                    ai-auto-complete-streaming-min-chunk-size)
                                 (> (float-time (time-subtract current-time
                                                              ai-auto-complete-native-streaming-last-update-time))
                                    ai-auto-complete-streaming-max-update-frequency))
                          ;; Update the streaming display
                          (ai-auto-complete-streaming-update ai-auto-complete-native-streaming-accumulated-content)

                          ;; Reset accumulated content
                          (setq ai-auto-complete-native-streaming-accumulated-content "")

                          ;; Update timestamp
                          (setq ai-auto-complete-native-streaming-last-update-time current-time)))

                      ;; Call the callback if provided
                      (when (functionp ai-auto-complete-native-streaming-callback)
                        (funcall ai-auto-complete-native-streaming-callback content))))
                (error nil)))))))))

(defun ai-auto-complete-native-streaming-anthropic-sentinel (process event)
  "Sentinel function for Anthropic streaming process.
PROCESS is the streaming process.
EVENT is the process event."
  (cond
   ((string-match "finished" event)
    ;; Process completed successfully
    ;; Update any remaining accumulated content
    (when (and (boundp 'ai-auto-complete-native-streaming-accumulated-content)
               (not (string-empty-p ai-auto-complete-native-streaming-accumulated-content)))
      (ai-auto-complete-streaming-update ai-auto-complete-native-streaming-accumulated-content)
      (setq ai-auto-complete-native-streaming-accumulated-content ""))

    (ai-auto-complete-streaming-complete)

    ;; Clean up
    (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
      (kill-buffer ai-auto-complete-native-streaming-buffer))

    (setq ai-auto-complete-native-streaming-process nil
          ai-auto-complete-native-streaming-buffer nil
          ai-auto-complete-native-streaming-callback nil
          ai-auto-complete-native-streaming-error-callback nil))

   ((string-match "exited abnormally" event)
    ;; Process failed
    (let ((error-message "Anthropic streaming failed"))
      ;; Get the error message from the buffer if possible
      (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
        (with-current-buffer ai-auto-complete-native-streaming-buffer
          (setq error-message (buffer-string))
          (kill-buffer)))

      ;; Call the error callback if provided
      (when (functionp ai-auto-complete-native-streaming-error-callback)
        (funcall ai-auto-complete-native-streaming-error-callback error-message))

      ;; Complete the streaming with an error message
      (ai-auto-complete-streaming-update (format "\n\nError: %s" error-message))
      (ai-auto-complete-streaming-complete)

      ;; Clean up
      (setq ai-auto-complete-native-streaming-process nil
            ai-auto-complete-native-streaming-buffer nil
            ai-auto-complete-native-streaming-callback nil
            ai-auto-complete-native-streaming-error-callback nil)))))

;; Helper function to format the prompt for OpenAI
(defun ai-auto-complete-native-streaming-format-prompt (prompt)
  "Format PROMPT for OpenAI streaming."
  (cond
   ((listp prompt)
    ;; Prompt is already a list of messages
    prompt)
   ((stringp prompt)
    ;; Prompt is a string, convert to a single user message
    `(((role . "user") (content . ,prompt))))
   (t
    ;; Default to an empty prompt
    `(((role . "user") (content . ""))))))

;; Helper function to format the prompt for Anthropic
(defun ai-auto-complete-native-streaming-format-anthropic-prompt (prompt)
  "Format PROMPT for Anthropic streaming."
  (cond
   ((listp prompt)
    ;; Convert OpenAI-style messages to Anthropic format
    (mapcar (lambda (msg)
              (let ((role (cdr (assoc 'role msg)))
                    (content (cdr (assoc 'content msg))))
                (cond
                 ((string= role "user")
                  `((role . "user") (content . ,content)))
                 ((string= role "assistant")
                  `((role . "assistant") (content . ,content)))
                 ((string= role "system")
                  ;; Anthropic handles system messages differently
                  nil)
                 (t
                  `((role . "user") (content . ,content))))))
            prompt))
   ((stringp prompt)
    ;; Prompt is a string, convert to a single user message
    `(((role . "user") (content . ,prompt))))
   (t
    ;; Default to an empty prompt
    `(((role . "user") (content . ""))))))

;; Function to cancel streaming
(defun ai-auto-complete-native-streaming-cancel ()
  "Cancel the current streaming process."
  (interactive)
  (when (and ai-auto-complete-native-streaming-process
             (process-live-p ai-auto-complete-native-streaming-process))
    ;; Kill the process
    (delete-process ai-auto-complete-native-streaming-process)

    ;; Clean up the buffer
    (when (buffer-live-p ai-auto-complete-native-streaming-buffer)
      (kill-buffer ai-auto-complete-native-streaming-buffer))

    ;; Update the streaming display
    (ai-auto-complete-streaming-update "\n\n[Cancelled]")
    (ai-auto-complete-streaming-complete)

    ;; Reset the streaming state
    (setq ai-auto-complete-native-streaming-process nil
          ai-auto-complete-native-streaming-buffer nil
          ai-auto-complete-native-streaming-callback nil
          ai-auto-complete-native-streaming-error-callback nil)

    (message "Streaming cancelled")))

(provide 'ui/native-streaming)
;;; native-streaming.el ends here
