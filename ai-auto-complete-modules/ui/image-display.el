;;; image-display.el --- Image display for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides image display functionality for the chat interface.
;; It allows for displaying images inline in the chat buffer.

;;; Code:

(require 'cl-lib)
(require 'image)

;; Customization options for image display
(defgroup ai-auto-complete-image-display nil
  "Settings for image display in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-image-display-")

(defcustom ai-auto-complete-image-display-max-width 600
  "Maximum width of displayed images in pixels."
  :type 'integer
  :group 'ai-auto-complete-image-display)

(defcustom ai-auto-complete-image-display-max-height 400
  "Maximum height of displayed images in pixels."
  :type 'integer
  :group 'ai-auto-complete-image-display)

(defcustom ai-auto-complete-image-display-inline t
  "Whether to display images inline in the chat buffer."
  :type 'boolean
  :group 'ai-auto-complete-image-display)

;; Variables for tracking image display state
(defvar-local ai-auto-complete-image-display-images nil
  "List of images displayed in the current buffer.")

;; Main function to display an image
(defun ai-auto-complete-image-display-insert (file-or-url &optional alt-text)
  "Insert an image from FILE-OR-URL at point.
ALT-TEXT is optional alternative text to display if the image cannot be loaded."
  (if (and (fboundp 'image-type-available-p)
           (display-images-p))
      (let* ((image-file (if (string-match-p "^https?://" file-or-url)
                            (ai-auto-complete-image-display-download-image file-or-url)
                          file-or-url))
             (alt (or alt-text (format "Image: %s" (file-name-nondirectory image-file))))
             (image (when (and image-file (file-exists-p image-file))
                      (create-image image-file nil nil
                                   :max-width ai-auto-complete-image-display-max-width
                                   :max-height ai-auto-complete-image-display-max-height))))
        (if image
            (insert-image image alt)
          (insert (propertize (format "[Image not available: %s]" alt)
                             'face '(:slant italic :foreground "#888a85")))))
    ;; Images not supported, just insert alt text
    (insert (propertize (format "[Image: %s]" (or alt-text file-or-url))
                       'face '(:slant italic :foreground "#888a85")))))

;; Function to download an image from a URL
(defun ai-auto-complete-image-display-download-image (url)
  "Download an image from URL and return the local file path."
  (let* ((temp-dir (expand-file-name "ai-auto-complete-images" temporary-file-directory))
         (url-hash (md5 url))
         (file-extension (or (file-name-extension url) "png"))
         (local-file (expand-file-name (format "%s.%s" url-hash file-extension) temp-dir)))
    
    ;; Create the temporary directory if it doesn't exist
    (unless (file-exists-p temp-dir)
      (make-directory temp-dir t))
    
    ;; Download the image if it doesn't exist locally
    (unless (file-exists-p local-file)
      (condition-case err
          (url-copy-file url local-file t)
        (error (message "Error downloading image: %s" (error-message-string err)))))
    
    ;; Return the local file path
    (if (file-exists-p local-file)
        local-file
      nil)))

;; Function to parse and render markdown image syntax
(defun ai-auto-complete-image-display-render-markdown-images ()
  "Parse and render markdown image syntax in the current buffer."
  (goto-char (point-min))
  (while (re-search-forward "!\\[\\(.*?\\)\\](\\(.*?\\))" nil t)
    (let ((alt-text (match-string 1))
          (image-url (match-string 2))
          (start (match-beginning 0))
          (end (match-end 0)))
      
      ;; Replace the markdown syntax with the actual image
      (delete-region start end)
      (goto-char start)
      
      ;; Insert the image
      (ai-auto-complete-image-display-insert image-url alt-text))))

;; Function to clear temporary images
(defun ai-auto-complete-image-display-clear-temp-images ()
  "Clear temporary images downloaded by the image display module."
  (interactive)
  (let ((temp-dir (expand-file-name "ai-auto-complete-images" temporary-file-directory)))
    (when (file-exists-p temp-dir)
      (dolist (file (directory-files temp-dir t "\\.[a-z]+$"))
        (when (file-regular-p file)
          (delete-file file)))
      (message "Cleared temporary images"))))

;; Add hook to clear temporary images when Emacs exits
(add-hook 'kill-emacs-hook #'ai-auto-complete-image-display-clear-temp-images)

(provide 'ui/image-display)
;;; image-display.el ends here
