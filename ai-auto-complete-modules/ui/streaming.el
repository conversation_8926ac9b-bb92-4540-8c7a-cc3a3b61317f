;;; streaming.el --- Streaming support for chat interface -*- lexical-binding: t -*-

;;; Commentary:
;; This file provides streaming support for the chat interface.
;; It allows for real-time updates of responses and tool calls.

;;; Code:

(require 'cl-lib)
(require 'ui/markdown-renderer)

;; Customization options for streaming
(defgroup ai-auto-complete-streaming nil
  "Settings for streaming in AI Auto Complete."
  :group 'ai-auto-complete
  :prefix "ai-auto-complete-streaming-")

(defcustom ai-auto-complete-streaming-enabled t
  "Enable streaming for responses and tool calls."
  :type 'boolean
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-update-interval 0.1
  "Interval in seconds for updating the streaming display."
  :type 'float
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-typing-speed 30
  "Simulated typing speed in characters per second for non-streaming backends."
  :type 'integer
  :group 'ai-auto-complete-streaming)

;; Variables for tracking streaming state
(defvar-local ai-auto-complete-streaming-in-progress nil
  "Whether streaming is currently in progress.")

(defvar-local ai-auto-complete-streaming-timer nil
  "Timer for updating the streaming display.")

(defvar-local ai-auto-complete-streaming-buffer ""
  "Buffer for accumulating streaming content.")

(defvar-local ai-auto-complete-streaming-position nil
  "Position marker for the current streaming insertion point.")

(defvar-local ai-auto-complete-streaming-role nil
  "Role of the current streaming message (user, assistant, agent, etc.).")

(defvar-local ai-auto-complete-streaming-agent nil
  "Agent name for the current streaming message, if applicable.")

(defvar-local ai-auto-complete-streaming-tool-calls nil
  "List of tool calls detected during streaming.")

(defvar-local ai-auto-complete-streaming-tool-results nil
  "List of tool results received during streaming.")

(defvar-local ai-auto-complete-streaming-timestamps nil
  "Alist of timestamps for streaming events.")

;; Functions for streaming support

(defun ai-auto-complete-streaming-start (role &optional agent-name)
  "Start streaming a message with ROLE.
If ROLE is 'agent, AGENT-NAME should be provided."
  (when ai-auto-complete-streaming-enabled
    (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
      ;; Cancel any existing streaming timer
      (when ai-auto-complete-streaming-timer
        (cancel-timer ai-auto-complete-streaming-timer)
        (setq ai-auto-complete-streaming-timer nil))

      ;; Set up streaming state
      (setq ai-auto-complete-streaming-in-progress t
            ai-auto-complete-streaming-buffer ""
            ai-auto-complete-streaming-role role
            ai-auto-complete-streaming-agent agent-name
            ai-auto-complete-streaming-tool-calls nil
            ai-auto-complete-streaming-tool-results nil
            ai-auto-complete-streaming-timestamps nil)

      ;; Add initial timestamp
      (push (cons 'start (current-time)) ai-auto-complete-streaming-timestamps)

      ;; Insert the message prefix based on role
      (let ((inhibit-read-only t))
        (goto-char (point-max))
        ;; If there's an input marker, delete from there to the end
        (when (and ai-auto-complete--chat-input-marker
                   (marker-position ai-auto-complete--chat-input-marker))
          (delete-region ai-auto-complete--chat-input-marker (point-max)))

        ;; Insert the appropriate prefix based on role
        (cond
         ((eq role 'user)
          (insert (propertize ai-auto-complete-chat-prompt-prefix
                             'face 'ai-auto-complete-user-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'assistant)
          (insert (propertize ai-auto-complete-chat-response-prefix
                             'face 'ai-auto-complete-assistant-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'agent)
          (insert (propertize (format "AGENT-%s: " agent-name)
                             'face 'ai-auto-complete-agent-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'tool)
          (insert (propertize "TOOL: "
                             'face 'ai-auto-complete-tool-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'tool-result)
          (insert (propertize "RESULT: "
                             'face 'ai-auto-complete-tool-result-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t)))
         ((eq role 'emacs)
          (insert (propertize "emacs: "
                             'face 'ai-auto-complete-system-face
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))))

        ;; Set the streaming position marker
        (setq ai-auto-complete-streaming-position (point-marker))))))

(defun ai-auto-complete-streaming-update (chunk)
  "Update the streaming display with CHUNK of text."
  (when (and ai-auto-complete-streaming-enabled ai-auto-complete-streaming-in-progress)
    (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
      ;; Append the chunk to the streaming buffer
      (setq ai-auto-complete-streaming-buffer
            (concat ai-auto-complete-streaming-buffer chunk))

      ;; Update the display
      (let ((inhibit-read-only t))
        (save-excursion
          (goto-char ai-auto-complete-streaming-position)
          (delete-region ai-auto-complete-streaming-position (point-max))
          (insert (propertize ai-auto-complete-streaming-buffer
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))
          (setq ai-auto-complete-streaming-position (point-marker))))

      ;; Check for tool calls in the updated buffer
      (ai-auto-complete-streaming-check-for-tool-calls))))

(defun ai-auto-complete-streaming-check-for-tool-calls ()
  "Check for tool calls in the streaming buffer and execute them."
  (when (and ai-auto-complete-streaming-enabled
             ai-auto-complete-streaming-in-progress
             (boundp 'ai-auto-complete-tools-enabled)
             ai-auto-complete-tools-enabled)
    ;; Look for tool call patterns in the buffer
    (let ((buffer ai-auto-complete-streaming-buffer)
          (tool-calls nil))
      ;; Simple pattern matching for tool calls
      (with-temp-buffer
        (insert buffer)
        (goto-char (point-min))
        (while (re-search-forward "<tool name=\"\\([^\"]+\\)\">" nil t)
          (let ((tool-name (match-string 1))
                (start-pos (match-beginning 0)))
            ;; Try to find the closing tag
            (when (re-search-forward "</tool>" nil t)
              (let ((end-pos (match-end 0))
                    (tool-text (buffer-substring-no-properties start-pos (match-end 0))))
                ;; Extract parameters
                (goto-char start-pos)
                (when (re-search-forward "<parameters>\\(.*?\\)</parameters>" end-pos t)
                  (let ((params-text (match-string 1)))
                    ;; Try to parse parameters as JSON
                    (condition-case nil
                        (let ((params (json-read-from-string params-text)))
                          ;; Add to tool calls if not already processed
                          (unless (member (cons tool-name params) ai-auto-complete-streaming-tool-calls)
                            (push (cons tool-name params) tool-calls)
                            (push (cons tool-name params) ai-auto-complete-streaming-tool-calls)))
                      (error nil))))))))

      ;; Execute any new tool calls
      (dolist (tool-call (nreverse tool-calls))
        (let ((tool-name (car tool-call))
              (params (cdr tool-call)))
          ;; Add timestamp for tool call
          (push (cons (intern (format "tool-call-%s" tool-name)) (current-time))
                ai-auto-complete-streaming-timestamps)

          ;; Display the tool call
          (ai-auto-complete-streaming-display-tool-call tool-name params)

          ;; Execute the tool if it exists
          (when (and (boundp 'ai-auto-complete-tools)
                     (hash-table-p ai-auto-complete-tools)
                     (gethash tool-name ai-auto-complete-tools))
            (let ((tool (gethash tool-name ai-auto-complete-tools))
                  (tool-fn (gethash tool-name ai-auto-complete-tools)))
              (when (and tool (plist-get tool :function))
                (let ((fn (plist-get tool :function)))
                  (funcall fn params
                           (lambda (result)
                             ;; Add timestamp for tool result
                             (push (cons (intern (format "tool-result-%s" tool-name)) (current-time))
                                   ai-auto-complete-streaming-timestamps)

                             ;; Display the tool result
                             (ai-auto-complete-streaming-display-tool-result tool-name result)

                             ;; Add to tool results
                             (push (cons tool-name result) ai-auto-complete-streaming-tool-results)))))))))))))

(defun ai-auto-complete-streaming-display-tool-call (tool-name params)
  "Display a tool call in the chat buffer.
TOOL-NAME is the name of the tool being called.
PARAMS is a plist, alist, or hash-table of arguments for the tool."
  (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
    (let ((inhibit-read-only t)
          (args-str ""))
      ;; Format the arguments based on the type of params
      (cond
       ;; Hash table
       ((hash-table-p params)
        (maphash (lambda (key value)
                   (setq args-str (concat args-str
                                         (format "\n  %s: %s" key value))))
                 params))
       ;; Association list (alist)
       ((and (listp params) (consp (car-safe params)))
        (dolist (pair params)
          (setq args-str (concat args-str
                                (format "\n  %s: %s" (car pair) (cdr pair))))))
       ;; Property list (plist)
       ((listp params)
        (let ((key-values params))
          (while key-values
            (when (and (car key-values) (cadr key-values))
              (setq args-str (concat args-str
                                    (format "\n  %s: %s" (car key-values) (cadr key-values)))))
            (setq key-values (cddr key-values)))))
       ;; Other types (just convert to string)
       (t
        (setq args-str (concat args-str (format "\n  %s" params)))))

      ;; Display the tool call
      (save-excursion
        (goto-char (point-max))
        (insert (propertize "\n\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
        (insert (propertize "TOOL CALL: "
                           'face 'ai-auto-complete-tool-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))
        (insert (propertize (format "%s%s" tool-name args-str)
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))))))

(defun ai-auto-complete-streaming-display-tool-result (tool-name result)
  "Display a tool result in the chat buffer.
TOOL-NAME is the name of the tool that was called.
RESULT is the result returned by the tool."
  (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
    (let ((inhibit-read-only t)
          (line-count (with-temp-buffer
                        (insert result)
                        (count-lines (point-min) (point-max))))
          (make-collapsible (and (fboundp 'ai-auto-complete-collapsible-create)
                                (> line-count ai-auto-complete-collapsible-max-lines))))
      ;; Display the tool result
      (save-excursion
        (goto-char (point-max))
        (insert (propertize "\n\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
        (insert (propertize "TOOL RESULT: "
                           'face 'ai-auto-complete-tool-result-face
                           'read-only t
                           'front-sticky t
                           'rear-nonsticky t))

        ;; Format the result
        (let ((formatted-result (format "%s\n%s" tool-name result)))
          ;; Make it collapsible if needed
          (if make-collapsible
              (insert (ai-auto-complete-collapsible-create
                      (format "Tool Result: %s (%d lines)" tool-name line-count)
                      formatted-result
                      'tool-result
                      'expanded))
            ;; Otherwise just insert the formatted result
            (insert (propertize formatted-result
                               'read-only t
                               'front-sticky t
                               'rear-nonsticky t))))))))

(defun ai-auto-complete-streaming-complete ()
  "Complete the streaming process and finalize the message."
  (when (and ai-auto-complete-streaming-enabled ai-auto-complete-streaming-in-progress)
    (with-current-buffer (get-buffer-create ai-auto-complete-chat-buffer-name)
      ;; Add completion timestamp
      (push (cons 'complete (current-time)) ai-auto-complete-streaming-timestamps)

      ;; Cancel any existing streaming timer
      (when ai-auto-complete-streaming-timer
        (cancel-timer ai-auto-complete-streaming-timer)
        (setq ai-auto-complete-streaming-timer nil))

      ;; Apply markdown rendering to the final content
      (let ((inhibit-read-only t)
            (rendered-content (ai-auto-complete-markdown-render ai-auto-complete-streaming-buffer)))
        (save-excursion
          (goto-char ai-auto-complete-streaming-position)
          (delete-region ai-auto-complete-streaming-position (point-max))
          (insert (propertize rendered-content
                             'read-only t
                             'front-sticky t
                             'rear-nonsticky t))))

      ;; Add the message to history
      (when (boundp 'ai-auto-complete--chat-history)
        (cond
         ((eq ai-auto-complete-streaming-role 'user)
          (push (cons 'user ai-auto-complete-streaming-buffer) ai-auto-complete--chat-history))
         ((eq ai-auto-complete-streaming-role 'assistant)
          (push (cons 'assistant ai-auto-complete-streaming-buffer) ai-auto-complete--chat-history))
         ((eq ai-auto-complete-streaming-role 'agent)
          (push (cons 'agent (cons ai-auto-complete-streaming-agent ai-auto-complete-streaming-buffer))
                ai-auto-complete--chat-history))))

      ;; Add tool calls and results to history if any
      (when (and (boundp 'ai-auto-complete--chat-history)
                 ai-auto-complete-streaming-tool-calls)
        (let ((tool-history-entry
               (format "Tool calls executed:\n%s\n\n%s"
                       (mapconcat (lambda (tool-call)
                                    (format "- %s: %s"
                                            (car tool-call)
                                            (json-encode (cdr tool-call))))
                                  ai-auto-complete-streaming-tool-calls
                                  "\n")
                       (if ai-auto-complete-streaming-tool-results
                           "All tool calls completed successfully."
                         "No tool results received."))))
          (push (cons 'tool-result tool-history-entry) ai-auto-complete--chat-history)))

      ;; Reset streaming state
      (setq ai-auto-complete-streaming-in-progress nil
            ai-auto-complete-streaming-buffer ""
            ai-auto-complete-streaming-position nil
            ai-auto-complete-streaming-role nil
            ai-auto-complete-streaming-agent nil
            ai-auto-complete-streaming-tool-calls nil
            ai-auto-complete-streaming-tool-results nil)

      ;; Restore the input marker
      (goto-char (point-max))
      (insert (propertize "\n\n" 'read-only t 'front-sticky t 'rear-nonsticky t))
      (insert (propertize ai-auto-complete-chat-prompt-prefix
                         'face 'ai-auto-complete-user-face
                         'read-only t
                         'front-sticky t
                         'rear-nonsticky t))
      (setq ai-auto-complete--chat-input-marker (point-marker))
      (put-text-property (point) (point) 'read-only nil))))

(defcustom ai-auto-complete-streaming-min-chunk-size 100
  "Minimum number of characters to accumulate before updating the display.
Larger values will result in less frequent updates but may feel less responsive."
  :type 'integer
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-max-update-frequency 0.2
  "Maximum frequency of streaming updates in seconds.
Lower values will update more frequently but may cause performance issues."
  :type 'float
  :group 'ai-auto-complete-streaming)

(defcustom ai-auto-complete-streaming-smart-mode t
  "Whether to use smart streaming mode.
When enabled, streaming will adapt based on response size and speed."
  :type 'boolean
  :group 'ai-auto-complete-streaming)

(defvar ai-auto-complete-streaming-last-update-time nil
  "Timestamp of the last streaming update.")

(defvar ai-auto-complete-streaming-accumulated-text ""
  "Text accumulated since the last streaming update.")

(defun ai-auto-complete-streaming-simulate (text &optional role agent-name)
  "Simulate streaming for TEXT with ROLE and optional AGENT-NAME.
This is used for backends that don't support native streaming."
  (when ai-auto-complete-streaming-enabled
    ;; Start streaming
    (ai-auto-complete-streaming-start (or role 'assistant) agent-name)

    ;; Reset streaming variables
    (setq ai-auto-complete-streaming-last-update-time nil)
    (setq ai-auto-complete-streaming-accumulated-text "")

    ;; Determine if we should use smart streaming based on text length
    (let* ((use-smart-streaming (and ai-auto-complete-streaming-smart-mode
                                    (> (length text) (* 5 ai-auto-complete-streaming-min-chunk-size))))
           (text-length (length text))
           (is-short-response (< text-length 500))
           (is-code-block (string-match-p "```" text))
           (update-interval (cond
                             ;; For very short responses, just display immediately
                             (is-short-response 0)
                             ;; For code blocks, use slightly faster updates
                             (is-code-block 0.1)
                             ;; Otherwise use the configured interval
                             (t ai-auto-complete-streaming-max-update-frequency)))
           (chunk-size (cond
                        ;; For short responses, use the entire text
                        (is-short-response text-length)
                        ;; For code blocks, use larger chunks
                        (is-code-block (* 2 ai-auto-complete-streaming-min-chunk-size))
                        ;; Otherwise use the configured chunk size
                        (t ai-auto-complete-streaming-min-chunk-size)))
           (chunks (ai-auto-complete-streaming-split-text text chunk-size))
           (index 0))

      ;; If it's a very short response or smart streaming is disabled, just display it all at once
      (if (or is-short-response (not use-smart-streaming))
          (progn
            (ai-auto-complete-streaming-update text)
            (ai-auto-complete-streaming-complete))

        ;; Otherwise, set up a timer for smart streaming
        (setq ai-auto-complete-streaming-timer
              (run-with-timer 0 update-interval
                             (lambda ()
                               (if (< index (length chunks))
                                   (let ((current-time (current-time))
                                         (chunk (nth index chunks)))
                                     ;; Accumulate text
                                     (setq ai-auto-complete-streaming-accumulated-text
                                           (concat ai-auto-complete-streaming-accumulated-text chunk))

                                     ;; Update if enough time has passed or we've accumulated enough text
                                     (when (or (not ai-auto-complete-streaming-last-update-time)
                                              (> (length ai-auto-complete-streaming-accumulated-text)
                                                 ai-auto-complete-streaming-min-chunk-size)
                                              (> (float-time (time-subtract current-time
                                                                           ai-auto-complete-streaming-last-update-time))
                                                 ai-auto-complete-streaming-max-update-frequency))
                                       (ai-auto-complete-streaming-update ai-auto-complete-streaming-accumulated-text)
                                       (setq ai-auto-complete-streaming-accumulated-text "")
                                       (setq ai-auto-complete-streaming-last-update-time current-time))

                                     (setq index (1+ index)))
                                 ;; All chunks processed, complete streaming
                                 (progn
                                   ;; Update any remaining accumulated text
                                   (when (not (string-empty-p ai-auto-complete-streaming-accumulated-text))
                                     (ai-auto-complete-streaming-update ai-auto-complete-streaming-accumulated-text))

                                   (ai-auto-complete-streaming-complete)
                                   (cancel-timer ai-auto-complete-streaming-timer)
                                   (setq ai-auto-complete-streaming-timer nil))))))))))

(defun ai-auto-complete-streaming-split-text (text chunk-size)
  "Split TEXT into chunks of CHUNK-SIZE characters."
  (let ((chunks '())
        (start 0)
        (len (length text)))
    (while (< start len)
      (push (substring text start (min len (+ start chunk-size))) chunks)
      (setq start (+ start chunk-size)))
    (nreverse chunks)))

(provide 'ui/streaming)
;;; streaming.el ends here
